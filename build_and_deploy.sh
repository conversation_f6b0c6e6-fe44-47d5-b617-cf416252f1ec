#!/bin/sh

# 🔧 Настройки
CLIENT_REPO_SSH="**************:chainmatic/client-dr-pfoten-site.git"
BUILD_DIR="dist/platform/browser"
TMP_DIR=".coolify-deploy-tmp"

start_time=$(date +%s)

echo "🔨 Building Angular project..."
ng build --configuration production --base-href=/

echo "📦 Preparing temporary deploy folder..."
rm -rf $TMP_DIR
mkdir -p $TMP_DIR
cp -r $BUILD_DIR/* $TMP_DIR

cd $TMP_DIR

echo "🚀 Initializing Git repo for deploy..."
git init
git remote add origin $CLIENT_REPO_SSH
git checkout -b main

git add .
git commit -m "Deploy $(date '+%Y-%m-%d %H:%M:%S')"
git push --force origin main

cd ..
rm -rf $TMP_DIR

end_time=$(date +%s)
elapsed_time=$((end_time - start_time))
minutes=$((elapsed_time / 60))
seconds=$((elapsed_time % 60))

echo "✅ Angular build deployed to $CLIENT_REPO_SSH in $minutes min $seconds sec."
