export interface CustomerSupabaseConfig {
  url: string;
  anonKey: string;
  // Future: serviceKey for admin operations
}

export interface CustomerFeatureConfig {
  id: string;
  enabled: boolean;
  // Future: customer-specific feature settings
  settings?: Record<string, any>;
}

export interface CustomerConfig {
  name: string;
  supabase: CustomerSupabaseConfig;
  features: CustomerFeatureConfig[];
  // Future: customer-specific branding, limits, etc.
  branding?: {
    primaryColor?: string;
    secondaryColor?: string;
    logo?: string;
  };
}
