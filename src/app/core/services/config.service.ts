import { Injectable } from '@angular/core';
import type { PortalConfig, PortalFeature, SupabaseConfig } from '../../types';
import { portalConfig } from '../../portal.config';
import { defaultCustomerConfig, CustomerConfig, PortalFeature as CustomerPortalFeature } from '../../../customers';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  private readonly portalConfig: PortalConfig = portalConfig;
  private readonly customerConfig: CustomerConfig = defaultCustomerConfig;

  constructor() {}

  get supabase(): SupabaseConfig {
    return this.customerConfig.supabase;
  }

  get navigation(): PortalFeature[] {
    // Combine portal feature definitions with customer enabled features
    return this.portalConfig.features
      .filter(feature => this.customerConfig.enabledFeatures.includes(feature.id as CustomerPortalFeature))
      .map(feature => ({
        ...feature,
        enabled: true, // For backward compatibility
      }));
  }
}
